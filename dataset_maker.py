import os
import shutil

# === Chemins hardcodés ===
BASE_DIR = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\images from nde"  # <-- racine de ton dataset
OUT_IMAGES_DIR = r"C:\Users\<USER>\Documents\output\imagesTr"
OUT_IMAGES_DIR_UINT8 = r"C:\Users\<USER>\Documents\output\imagesTr_uint8"
OUT_LABELS_DIR = r"C:\Users\<USER>\Documents\output\labelsTr"

# Crée les dossiers de sortie s'ils n'existent pas
os.makedirs(OUT_IMAGES_DIR, exist_ok=True)
os.makedirs(OUT_IMAGES_DIR_UINT8, exist_ok=True)
os.makedirs(OUT_LABELS_DIR, exist_ok=True)

# === Parcours récursif ===
for root, dirs, files in os.walk(BASE_DIR):
    if os.path.basename(root) == "masks_visual":
        parent_dir = os.path.dirname(root)
        grandparent_dir = os.path.basename(os.path.dirname(parent_dir))
        image_dir_rgb24 = parent_dir  # dossier avec les images correspondantes RGB24
        image_dir_uint8 = os.path.join(os.path.dirname(parent_dir), "endviews_uint8")  # dossier UINT8

        for filename in files:
            mask_path = os.path.join(root, filename)
            image_rgb_path = os.path.join(image_dir_rgb24, filename)
            image_uint8_path = os.path.join(image_dir_uint8, filename)

            if not os.path.exists(image_rgb_path):
                print(f"[⚠️] Image RGB manquante pour le masque: {filename}")
                continue

            if not os.path.exists(image_uint8_path):
                print(f"[⚠️] Image UINT8 manquante pour le masque: {filename}")
                continue

            # Nouveau nom avec préfixe du dossier grand-parent
            new_name = f"{grandparent_dir}__{filename}"

            # Chemins de destination
            out_mask = os.path.join(OUT_LABELS_DIR, new_name)
            out_image_rgb = os.path.join(OUT_IMAGES_DIR, new_name)
            out_image_uint8 = os.path.join(OUT_IMAGES_DIR_UINT8, new_name)

            shutil.copy2(mask_path, out_mask)
            shutil.copy2(image_rgb_path, out_image_rgb)
            shutil.copy2(image_uint8_path, out_image_uint8)

            print(f"[✔️] Copié : {new_name}")

print("✅ Terminé.")
